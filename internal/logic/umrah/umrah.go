package umrah

import (
	"context"
	"database/sql"
	"errors"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"
	"time"
)

type sUmrah struct{}

func init() {
	service.RegisterUmrah(New())
}

func New() *sUmrah {
	return &sUmrah{}
}

// 新增朝觐
func (s *sUmrah) UrutanManasikUmrahAdd(ctx context.Context, req *v1.UrutanManasikUmrahAddReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if "" == req.IconUrl {
		return out, gerror.New("图标不能为空")
	}
	if 0 >= req.UrutanNo {
		return out, gerror.New("朝觐顺序不能为空")
	}
	if 0 >= len(req.ContentArr) {
		return out, gerror.New("内容不能为空")
	}
	for _, item := range req.ContentArr {
		if "" == item.UrutanName {
			return out, gerror.New("仪式名称不能为空")
		}
		if "" == item.UrutanContent {
			return out, gerror.New("仪式内容不能为空")
		}
	}
	// 判断顺序是否重复
	total, err := dao.UmrahUrutan.Ctx(ctx).Where(dao.UmrahUrutan.Columns().UrutanNo, req.UrutanNo).Count()
	if err != nil {
		return out, err
	}
	if total > 0 {
		return out, gerror.New("此编号已存在，请输入不同的顺序编号。")
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 写入表
	err = dao.UmrahUrutan.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		urutanData := entity.UmrahUrutan{
			UrutanNo:   gconv.Int(req.UrutanNo),
			IconUrl:    req.IconUrl,
			CreateTime: currentTime,
			UpdateTime: currentTime,
		}
		id, err1 := dao.UmrahUrutan.Ctx(ctx).Data(urutanData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 批量插入UmrahUrutanContent表
		contentList := g.List{}
		for _, item := range req.ContentArr {
			contentList = append(contentList, g.Map{
				dao.UmrahUrutanContent.Columns().UrutanId:      gconv.Uint(id),
				dao.UmrahUrutanContent.Columns().LanguageId:    item.LanguageType,
				dao.UmrahUrutanContent.Columns().UrutanName:    item.UrutanName,
				dao.UmrahUrutanContent.Columns().UrutanTime:    item.UrutanTime,
				dao.UmrahUrutanContent.Columns().UrutanContent: item.UrutanContent,
				dao.UmrahUrutanContent.Columns().CreateTime:    currentTime,
				dao.UmrahUrutanContent.Columns().UpdateTime:    currentTime,
			})
		}
		if 0 < len(contentList) {
			_, err1 = dao.UmrahUrutanContent.Ctx(ctx).Data(contentList).Insert()
		}
		return err1
	})
	return out, err
}

// 修改朝觐
func (s *sUmrah) UrutanManasikUmrahEdit(ctx context.Context, req *v1.UrutanManasikUmrahEditReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("id不能为空")
	}
	// 获取数据
	var urutan entity.UmrahUrutan
	err = dao.UmrahUrutan.Ctx(ctx).Where(dao.UmrahUrutan.Columns().Id, req.Id).Scan(&urutan)
	if err != nil {
		return out, err
	}
	if 0 >= urutan.Id {
		return out, gerror.New("数据不存在")
	}
	if "" == req.IconUrl {
		return out, gerror.New("图标不能为空")
	}
	if 0 >= req.UrutanNo {
		return out, gerror.New("朝觐顺序不能为空")
	}
	if 0 >= len(req.ContentArr) {
		return out, gerror.New("内容不能为空")
	}
	for _, item := range req.ContentArr {
		if "" == item.UrutanName {
			return out, gerror.New("仪式名称不能为空")
		}
		if "" == item.UrutanContent {
			return out, gerror.New("仪式内容不能为空")
		}
	}
	// 判断顺序是否重复
	total, err := dao.UmrahUrutan.Ctx(ctx).
		Where(dao.UmrahUrutan.Columns().UrutanNo, req.UrutanNo).
		WhereNot(dao.UmrahUrutan.Columns().Id, req.Id).
		Count()
	if err != nil {
		return out, err
	}
	if total > 0 {
		return out, gerror.New("此编号已存在，请输入不同的顺序编号。")
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 写入表
	err = dao.UmrahUrutan.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		urutanData := g.Map{
			dao.UmrahUrutan.Columns().UrutanNo:   gconv.Int(req.UrutanNo),
			dao.UmrahUrutan.Columns().IconUrl:    req.IconUrl,
			dao.UmrahUrutan.Columns().UpdateTime: currentTime,
		}
		_, err1 = dao.UmrahUrutan.Ctx(ctx).Where(dao.UmrahUrutan.Columns().Id, req.Id).Data(urutanData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.UmrahUrutanContent.Ctx(ctx).Where(dao.UmrahUrutanContent.Columns().UrutanId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 批量插入UmrahUrutanContent表
		contentList := g.List{}
		for _, item := range req.ContentArr {
			contentList = append(contentList, g.Map{
				dao.UmrahUrutanContent.Columns().UrutanId:      gconv.Uint(req.Id),
				dao.UmrahUrutanContent.Columns().LanguageId:    item.LanguageType,
				dao.UmrahUrutanContent.Columns().UrutanName:    item.UrutanName,
				dao.UmrahUrutanContent.Columns().UrutanTime:    item.UrutanTime,
				dao.UmrahUrutanContent.Columns().UrutanContent: item.UrutanContent,
				dao.UmrahUrutanContent.Columns().CreateTime:    urutan.CreateTime,
				dao.UmrahUrutanContent.Columns().UpdateTime:    currentTime,
			})
		}
		if 0 < len(contentList) {
			_, err1 = dao.UmrahUrutanContent.Ctx(ctx).Data(contentList).Insert()
		}
		return err1
	})
	return out, err
}

// 获取Urutan Manasik列表
func (s *sUmrah) UrutanManasikUmrahList(ctx context.Context, req *v1.UrutanManasikUmrahListReq) (out *v1.UrutanManasikUmrahListRes, err error) {
	out = new(v1.UrutanManasikUmrahListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.UmrahUrutan.Ctx(ctx)
	// 总数
	out.Total, err = orm.Count()
	if err != nil {
		return
	}
	if out.Total <= 0 {
		out.List = []v1.UrutanManasikUmrahListItem{}
		return
	}
	// 获取列表
	var list []entity.UmrahUrutan
	err = orm.Page(req.Current, req.PageSize).OrderAsc(dao.UmrahUrutan.Columns().UrutanNo).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.UrutanManasikUmrahListItem{}
		return
	}
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	// 提取id
	urutanIds := gutil.ListItemValuesUnique(list, "Id")
	// 获取内容
	var urutanContentList []entity.UmrahUrutanContent
	err = dao.UmrahUrutanContent.Ctx(ctx).
		WhereIn(dao.UmrahUrutanContent.Columns().UrutanId, urutanIds).
		Scan(&urutanContentList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.UrutanManasikUmrahListItem{}
		return
	}
	var languageArr = make([]v1.LanguageArrItem, 0)
	// 组成map
	urutanContentMap := make(map[int]map[int]entity.UmrahUrutanContent)
	for _, v := range urutanContentList {
		if _, ok := urutanContentMap[gconv.Int(v.UrutanId)]; !ok {
			urutanContentMap[gconv.Int(v.UrutanId)] = make(map[int]entity.UmrahUrutanContent)
		}
		urutanContentMap[gconv.Int(v.UrutanId)][gconv.Int(v.LanguageId)] = v
	}
	// 组装数据
	for _, urutan := range list {
		one := v1.UrutanManasikUmrahListItem{
			Id:          gconv.Uint(urutan.Id),
			UrutanNo:    gconv.Uint(urutan.UrutanNo),
			IconUrl:     urutan.IconUrl,
			LanguageArr: languageArr,
		}
		urutanContent, ok := urutanContentMap[gconv.Int(urutan.Id)][gconv.Int(currentLang)]
		if ok {
			one.UrutanName = urutanContent.UrutanName
			one.UrutanTime = urutanContent.UrutanTime
		}
		// 组装支持的语言
		for _, language := range []int{consts.Zero, consts.One, consts.Two} {
			// 判断是否存在
			_, ok = urutanContentMap[gconv.Int(urutan.Id)][language]
			var IsSupport int
			if ok {
				IsSupport = consts.One
			}
			one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
				LanguageType:     language,
				LanguageTypeText: consts.GetLangText(language),
				IsSupport:        IsSupport,
			})
		}
		out.List = append(out.List, one)
	}
	return
}

// 获取朝觐详情
func (s *sUmrah) UrutanManasikUmrahInfo(ctx context.Context, req *v1.UrutanManasikUmrahInfoReq) (out *v1.UrutanManasikUmrahInfoRes, err error) {
	out = new(v1.UrutanManasikUmrahInfoRes)
	// 获取朝觐详情
	var data entity.UmrahUrutan
	err = dao.UmrahUrutan.Ctx(ctx).Where(dao.UmrahUrutan.Columns().Id, req.Id).Scan(&data)
	if err != nil {
		return out, err
	}
	if 0 >= data.Id {
		return out, gerror.New("数据不存在")
	}
	// 获取朝觐内容
	var contentList []entity.UmrahUrutanContent
	err = dao.UmrahUrutanContent.Ctx(ctx).Where(dao.UmrahUrutanContent.Columns().UrutanId, req.Id).Scan(&contentList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	for _, v := range contentList {
		out.ContentArr = append(out.ContentArr, v1.UrutanManasikUmrahContentItem{
			LanguageType:  gconv.Int(v.LanguageId),
			UrutanName:    v.UrutanName,
			UrutanTime:    v.UrutanTime,
			UrutanContent: v.UrutanContent,
		})
	}
	out.IconUrl = data.IconUrl
	out.UrutanNo = gconv.Uint(data.UrutanNo)
	out.Id = gconv.Uint(data.Id)
	return
}

// Ringkas doa 列表
func (s *sUmrah) DoaRingkasUmrahList(ctx context.Context, req *v1.DoaRingkasUmrahListReq) (out *v1.DoaRingkasUmrahListRes, err error) {
	out = new(v1.DoaRingkasUmrahListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.HajiDoaRingkas.Ctx(ctx)
	// 获取总数
	out.Total, err = orm.Count()
	if err != nil {
		out.List = []v1.DoaRingkasUmrahListItem{}
		return out, err
	}
	if out.Total <= 0 {
		out.List = []v1.DoaRingkasUmrahListItem{}
		return out, nil
	}
	// 获取列表
	var data []entity.UmrahDoaRingkas
	err = orm.
		Page(req.Current, req.PageSize).
		OrderAsc(dao.UmrahDoaRingkas.Columns().DoaNo).
		Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	for _, item := range data {
		out.List = append(out.List, v1.DoaRingkasUmrahListItem{
			DoaName: item.DoaName,
			DoaNo:   item.DoaNo,
			Id:      gconv.Uint(item.Id),
		})
	}
	return out, nil
}

// Ringkas doa 详情
func (s *sUmrah) DoaRingkasUmrahInfo(ctx context.Context, req *v1.DoaRingkasUmrahInfoReq) (out *v1.DoaRingkasUmrahInfoRes, err error) {
	out = new(v1.DoaRingkasUmrahInfoRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("id不能为空")
	}
	// 查询数据是否存在
	var data entity.UmrahDoaRingkasContent
	err = dao.HajiDoaRingkas.Ctx(ctx).Where(dao.HajiDoaRingkas.Columns().Id, req.Id).Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	if data.Id == 0 {
		return out, gerror.New("数据不存在")
	}
	var list []entity.UmrahDoaRingkasContent
	err = dao.UmrahDoaRingkasContent.Ctx(ctx).
		Where(dao.UmrahDoaRingkasContent.Columns().DoaId, req.Id).
		OrderAsc(dao.UmrahDoaRingkasContent.Columns().ContentOrder).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	// 组装数据
	for _, v := range list {
		out.List = append(out.List, v1.DoaRingkasUmrahInfoItem{
			Title:          v.Title,
			MuqattaAt:      v.MuqattaAt,
			ArabicText:     v.ArabicText,
			IndonesianText: v.IndonesianText,
			LatinText:      v.LatinText,
		})
	}
	return out, nil
}

// Panjang 列表
func (s *sUmrah) DoaPanjangUmrahList(ctx context.Context, req *v1.DoaPanjangUmrahListReq) (out *v1.DoaPanjangUmrahListRes, err error) {
	out = new(v1.DoaPanjangUmrahListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	// 获取总数
	out.Total, err = dao.HajiDoaPanjang.Ctx(ctx).Count()
	if err != nil {
		out.List = []v1.DoaPanjangUmrahListItem{}
		return out, err
	}
	if out.Total <= 0 {
		out.List = []v1.DoaPanjangUmrahListItem{}
		return out, nil
	}
	var list []entity.UmrahDoaPanjang
	err = dao.HajiDoaPanjang.Ctx(ctx).
		OrderAsc(dao.HajiDoaPanjang.Columns().DoaNo).
		Page(req.Current, req.PageSize).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}
	for _, v := range list {
		out.List = append(out.List, v1.DoaPanjangUmrahListItem{
			BacaanCount: gconv.Int(v.BacaanCount),
			DoaName:     v.DoaName,
			DoaNo:       gconv.Int(v.DoaNo),
			Id:          gconv.Uint(v.Id),
		})
	}
	return out, nil
}

// Panjang Bacaan列表
func (s *sUmrah) DoaPanjangUmrahBacaanList(ctx context.Context, req *v1.DoaPanjangUmrahBacaanListReq) (out *v1.DoaPanjangUmrahBacaanListRes, err error) {
	out = new(v1.DoaPanjangUmrahBacaanListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	if req.Id == 0 {
		out.List = []v1.DoaPanjangUmrahBacaanListItem{}
		return out, gerror.New("id不能为空")
	}
	// 查询总数
	total, err := dao.HajiDoaPanjangBacaan.Ctx(ctx).
		Where(dao.HajiDoaPanjangBacaan.Columns().DoaId, req.Id).
		Count()
	if err != nil {
		return out, err
	}
	if total <= 0 {
		out.List = []v1.DoaPanjangUmrahBacaanListItem{}
		return out, nil
	}
	out.Total = total
	// 查询列表
	var list []entity.UmrahDoaPanjangBacaan
	err = dao.UmrahDoaPanjangBacaan.Ctx(ctx).
		Where(dao.UmrahDoaPanjangBacaan.Columns().DoaId, req.Id).
		OrderAsc(dao.UmrahDoaPanjangBacaan.Columns().BacaanNo).
		Page(req.Current, req.PageSize).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.DoaPanjangUmrahBacaanListItem{}
		return out, err
	}
	for _, v := range list {
		out.List = append(out.List, v1.DoaPanjangUmrahBacaanListItem{
			BacaanName: v.BacaanName,
			DoaNo:      v.BacaanNo,
			Id:         gconv.Uint(v.Id),
		})
	}
	return out, nil
}

// Panjang Bacaan列表详情
func (s *sUmrah) DoaPanjangUmrahBacaanListInfo(ctx context.Context, req *v1.DoaPanjangUmrahBacaanListInfoReq) (out *v1.DoaPanjangUmrahBacaanListInfoRes, err error) {
	out = new(v1.DoaPanjangUmrahBacaanListInfoRes)
	out.Current = req.Current
	out.Offset = req.Offset
	if req.Id == 0 {
		out.List = []v1.DoaPanjangUmrahBacaanListInfoItem{}
		return out, gerror.New("BacaanId不能为空")
	}
	orm := dao.UmrahDoaPanjangBacaanContent.Ctx(ctx)
	// 获取总数
	out.Total, err = orm.
		Where(dao.UmrahDoaPanjangBacaanContent.Columns().BacaanId, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取Bacaan总数失败")
	}
	if out.Total == 0 {
		out.List = []v1.DoaPanjangUmrahBacaanListInfoItem{}
		return out, nil
	}
	// 获取列表
	var list []entity.UmrahDoaPanjangBacaanContent
	err = orm.
		Where(dao.UmrahDoaPanjangBacaanContent.Columns().BacaanId, req.Id).
		Page(req.Current, req.PageSize).
		OrderAsc(dao.UmrahDoaPanjangBacaanContent.Columns().ContentOrder).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.DoaPanjangUmrahBacaanListInfoItem{}
		return out, gerror.Wrap(err, "获取列表失败")
	}
	for _, v := range list {
		out.List = append(out.List, v1.DoaPanjangUmrahBacaanListInfoItem{
			ArabicText:     v.ArabicText,
			IndonesianText: v.IndonesianText,
			LatinText:      v.LatinText,
			MuqattaAt:      v.MuqattaAt,
			Title:          v.Title,
		})
	}
	return out, nil
}

// Hikmah Umrah 列表
func (s *sUmrah) HikmahUmrahList(ctx context.Context, req *v1.HikmahUmrahListReq) (out *v1.HikmahUmrahListRes, err error) {
	out = new(v1.HikmahUmrahListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.UmrahHikmah.Ctx(ctx)
	// 获取总数
	total, err := orm.Count()
	if err != nil {
		out.List = []v1.HikmahUmrahListItem{}
		return out, err
	}
	if total <= 0 {
		out.List = []v1.HikmahUmrahListItem{}
		return out, nil
	}
	out.Total = total
	var list []entity.UmrahHikmah
	err = orm.Page(req.Current, req.PageSize).OrderAsc(dao.UmrahHikmah.Columns().SortOrder).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.HikmahUmrahListItem{}
		return out, err
	}
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	// 提取文章id
	articleIds := gutil.ListItemValuesUnique(list, "ArticleId")
	type article struct {
		ArticleId uint   `json:"article_id"`
		Name      string `json:"name"`
	}
	var articleList []article
	err = dao.NewsArticleLanguage.Ctx(ctx).
		WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, articleIds).
		Where(dao.NewsArticleLanguage.Columns().LanguageId, currentLang).
		Scan(&articleList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.HikmahUmrahListItem{}
		return out, err
	}
	var articleListMap = make(map[uint]string)
	for _, v := range articleList {
		articleListMap[v.ArticleId] = v.Name
	}
	// 提取id
	ids := gutil.ListItemValuesUnique(list, "Id")
	var languageList []entity.UmrahHikmahLanguages
	err = dao.UmrahHikmahLanguages.Ctx(ctx).Where(dao.UmrahHikmahLanguages.Columns().HikmahId, ids).Scan(&languageList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.HikmahUmrahListItem{}
		return out, err
	}
	var languageListMap = make(map[uint]map[int]entity.UmrahHikmahLanguages)
	for _, v := range languageList {
		if _, ok := languageListMap[gconv.Uint(v.HikmahId)]; !ok {
			languageListMap[gconv.Uint(v.HikmahId)] = make(map[int]entity.UmrahHikmahLanguages)
		}
		languageListMap[gconv.Uint(v.HikmahId)][gconv.Int(v.LanguageId)] = v
	}
	// 计算序号开始值
	serialNumber := (req.Current-1)*req.PageSize + 1
	for _, v := range list {
		articleName, ok := articleListMap[gconv.Uint(v.ArticleId)]
		if !ok {
			articleName = ""
		}
		var title string
		item, ok := languageListMap[gconv.Uint(v.Id)][gconv.Int(currentLang)]
		if ok {
			title = item.Title
		}
		one := v1.HikmahUmrahListItem{
			Id:          gconv.Uint(v.Id),
			Title:       title,
			ArticleId:   gconv.Uint(v.ArticleId),
			ArticleName: articleName,
			LanguageArr: []v1.LanguageArrItem{},
			SerialNum:   serialNumber,
			SortOrder:   gconv.Int(v.SortOrder),
		}
		var IsZh, IsEn, IsId int
		_, ok = languageListMap[gconv.Uint(v.Id)][consts.Zero]
		if ok {
			IsZh = consts.One
		}
		_, ok = languageListMap[gconv.Uint(v.Id)][consts.One]
		if ok {
			IsEn = consts.One
		}
		_, ok = languageListMap[gconv.Uint(v.Id)][consts.Two]
		if ok {
			IsId = consts.One
		}
		// 支持的语言
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Zero,
			LanguageTypeText: consts.ManLangZhStr,
			IsSupport:        IsZh,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: consts.ManLangEnStr,
			IsSupport:        IsEn,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: consts.ManLangIdStr,
			IsSupport:        IsId,
		})
		out.List = append(out.List, one)
		serialNumber++
	}
	return out, nil
}

// Hikmah Umrah 详情
func (s *sUmrah) HikmahUmrahInfo(ctx context.Context, req *v1.HikmahUmrahInfoReq) (out *v1.HikmahUmrahInfoRes, err error) {
	out = new(v1.HikmahUmrahInfoRes)
	var data entity.UmrahHikmah
	err = dao.UmrahHikmah.Ctx(ctx).Where(dao.UmrahHikmah.Columns().Id, req.Id).Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	var languageList []entity.UmrahHikmahLanguages
	err = dao.UmrahHikmahLanguages.Ctx(ctx).Where(dao.UmrahHikmahLanguages.Columns().HikmahId, req.Id).Scan(&languageList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	var titleArr = make([]v1.UmrahTitleArrItem, 0)
	for _, v := range languageList {
		titleArr = append(titleArr, v1.UmrahTitleArrItem{
			LanguageType: gconv.Int(v.LanguageId),
			Title:        v.Title,
		})
	}
	out.Id = req.Id
	out.TitleArr = titleArr
	out.SortOrder = gconv.Int(data.SortOrder)
	out.ArticleId = gconv.Uint(data.ArticleId)

	return out, nil
}

// Hikmah Umrah 新增
func (s *sUmrah) HikmahUmrahAdd(ctx context.Context, req *v1.HikmahUmrahAddReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= req.SortOrder || req.SortOrder > 9999 {
		return out, gerror.New("排序必须大于0且小于9999")
	}
	if len(req.TitleArr) <= 0 {
		return out, gerror.New("标题列表不能为空")
	}
	if 0 >= req.ArticleId {
		return out, gerror.New("关联文章id不能为空")
	}
	for _, item := range req.TitleArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return out, gerror.New("语言类型错误")
		}
		if "" == item.Title {
			return out, gerror.New("标题不能为空")
		}
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	var umrahHikmahLanguagesData = make([]entity.UmrahHikmahLanguages, 0)
	err = dao.HajiHikmah.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		data := entity.UmrahHikmah{
			ArticleId:  gconv.Uint64(req.ArticleId),
			SortOrder:  gconv.Uint(req.SortOrder),
			CreateTime: currentTime,
			UpdateTime: currentTime,
		}
		id, err1 := dao.UmrahHikmah.Ctx(ctx).Data(data).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 写入UmrahHikmahLanguage表
		for _, languageItem := range req.TitleArr {
			umrahHikmahLanguagesData = append(umrahHikmahLanguagesData, entity.UmrahHikmahLanguages{
				HikmahId:   gconv.Uint64(id),
				LanguageId: gconv.Uint(languageItem.LanguageType),
				Title:      languageItem.Title,
				CreateTime: currentTime,
				UpdateTime: currentTime,
			})
		}
		_, err1 = dao.UmrahHikmahLanguages.Ctx(ctx).Data(umrahHikmahLanguagesData).Insert()
		return err1
	})

	return out, nil
}

// Hikmah Umrah 编辑
func (s *sUmrah) HikmahUmrahEdit(ctx context.Context, req *v1.HikmahUmrahEditReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("id不能为空")
	}
	if 0 >= req.SortOrder || req.SortOrder > 9999 {
		return out, gerror.New("排序必须大于0且小于9999")
	}
	if len(req.TitleArr) <= 0 {
		return out, gerror.New("标题列表不能为空")
	}
	if 0 >= req.ArticleId {
		return out, gerror.New("关联文章id不能为空")
	}
	for _, item := range req.TitleArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return out, gerror.New("语言类型错误")
		}
		if "" == item.Title {
			return out, gerror.New("标题不能为空")
		}
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	var umrahHikmahLanguagesData = make([]entity.UmrahHikmahLanguages, 0)
	err = dao.UmrahHikmah.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		data := g.Map{
			dao.UmrahHikmah.Columns().ArticleId:  req.ArticleId,
			dao.UmrahHikmah.Columns().SortOrder:  req.SortOrder,
			dao.UmrahHikmah.Columns().UpdateTime: currentTime,
		}
		_, err1 = dao.UmrahHikmah.Ctx(ctx).Where(dao.UmrahHikmah.Columns().Id, req.Id).Data(data).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.UmrahHikmahLanguages.Ctx(ctx).Where(dao.UmrahHikmahLanguages.Columns().HikmahId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 写入HajiHikmahLanguage表
		for _, languageItem := range req.TitleArr {
			umrahHikmahLanguagesData = append(umrahHikmahLanguagesData, entity.UmrahHikmahLanguages{
				HikmahId:   gconv.Uint64(req.Id),
				LanguageId: gconv.Uint(languageItem.LanguageType),
				Title:      languageItem.Title,
				CreateTime: currentTime,
				UpdateTime: currentTime,
			})
		}
		_, err1 = dao.UmrahHikmahLanguages.Ctx(ctx).Data(umrahHikmahLanguagesData).Insert()
		return err1
	})

	return out, nil
}

// Hikmah Umrah 删除
func (s *sUmrah) HikmahUmrahDelete(ctx context.Context, req *v1.HikmahUmrahDeleteReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= len(req.Ids) {
		return out, gerror.New("id不能为空")
	}
	err = dao.UmrahHikmah.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.UmrahHikmah.Ctx(ctx).WhereIn(dao.UmrahHikmah.Columns().Id, req.Ids).Delete()
		if err1 != nil {
			return err1
		}
		// 物理删除
		_, err1 = dao.UmrahHikmahLanguages.Ctx(ctx).WhereIn(dao.UmrahHikmahLanguages.Columns().HikmahId, req.Ids).Delete()
		return err1
	})

	return out, nil
}
