package nu

import (
	"context"
	"database/sql"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/pkg/errors"
	v1 "gtcms/api/v1"
	dao "gtcms/internal/dao/user_account_svc"
	do "gtcms/internal/model/do/user_account_svc"
	entity "gtcms/internal/model/entity/user_account_svc"
	"gtcms/internal/service"
)

type sUserNu struct{}

func init() {
	service.RegisterUserNu(New())
}
func New() *sUserNu {
	return &sUserNu{}
}

func (f *sUserNu) UserNuList(ctx context.Context, req *v1.UserNuListReq) (res *v1.UserNuListRes, err error) {
	md := dao.UserNuId.Ctx(ctx)
	if req.RealName != "" {
		md = md.WhereLike(dao.UserNuId.Columns().RealName, "%"+req.RealName+"%")
	}

	if req.UserId > 0 {
		md = md.Where(dao.UserNuId.Columns().UserId, req.UserId)
	}

	if req.Gender != nil {
		md = md.Where(dao.UserNuId.Columns().Gender, req.Gender)
	}
	if req.Status > 0 {
		md = md.Where(dao.UserNuId.Columns().Status, req.Status)
	}
	if req.NickName != "" {
		userIdsModel := dao.UserInfo.Ctx(ctx).Where("nick_name like ?", "%"+req.NickName+"%").Fields("user_id")
		md = md.WhereIn(dao.UserNuId.Columns().UserId, userIdsModel)
	}
	if req.StartTime > 0 {
		md = md.WhereGTE(dao.UserNuId.Columns().ReviewedAt, req.StartTime).WhereLTE(dao.UserNuId.Columns().ReviewedAt, req.EndTime)
	}
	// todo 这个是不是该用区域id ?
	if req.WilayahPwnu != "" {
		md = md.Where(dao.UserNuId.Columns().WilayahPwnu, req.WilayahPwnu)
	}
	var userNus []*v1.UserNuListItem
	var total int

	err = md.Page(req.Current, req.PageSize).OrderDesc("id").ScanAndCount(&userNus, &total, false)
	if err != nil {
		return nil, err
	}

	var userInfos []*entity.UserInfo
	userIds := gutil.ListItemValuesUnique(userNus, "UserId")
	err = dao.UserInfo.Ctx(ctx).WhereIn(dao.UserInfo.Columns().UserId, userIds).Scan(&userInfos)
	if err != nil {
		return nil, err
	}
	userMap := make(map[uint64]*entity.UserInfo, len(userInfos))
	for _, u := range userInfos {
		userMap[u.UserId] = u
	}
	for _, u := range userNus {
		if _, ok := userMap[u.UserId]; ok {
			u.Nickname = userMap[u.UserId].Nickname
		}
	}
	res = &v1.UserNuListRes{
		List:    userNus,
		ListRes: v1.ListRes{Current: req.Current, Total: total},
	}

	return
}

func (f *sUserNu) UserNuAudit(ctx context.Context, req *v1.UserNuAuditReq) (res *v1.UserNuAuditRes, err error) {
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return nil, err
	}
	err = dao.UserNuAudit.Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

		userNuAudits := make([]do.UserNuAudit, 0, len(req.Ids))
		for _, id := range req.Ids {
			userNuAudits = append(userNuAudits, do.UserNuAudit{
				UserNuId:         id,
				AuditStatus:      req.AuditStatus,
				AuditReason:      req.AuditReason,
				AuditAccount:     admin.Account,
				AuditAccountName: admin.NickName,
				CreateTime:       gtime.TimestampMilli(),
			})
		}

		_, err = dao.UserNuAudit.Ctx(ctx).Save(userNuAudits)

		if err != nil {
			return err
		}

		_, err = dao.UserNuId.Ctx(ctx).WhereIn(dao.UserNuId.Columns().Id, req.Ids).Update(
			do.UserNuId{
				Status: req.AuditStatus,
			})
		return err
	})

	return
}

func (f *sUserNu) UserNuOne(ctx context.Context, req *v1.UserNuOneReq) (res *v1.UserNuOneRes, err error) {

	err = dao.UserNuId.Ctx(ctx).Where(dao.UserNuId.Columns().Id, req.Id).Scan(&res)
	if err != nil {
		return
	}

	var audit entity.UserNuAudit
	err = dao.UserNuAudit.Ctx(ctx).Where(dao.UserNuAudit.Columns().UserNuId, req.Id).Scan(&audit)
	// 未找到会报错sql.ErrNoRows 但是一开始都是没有审核记录的，没有审核记录是可以继续往下走的。
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, gerror.Wrap(err, "")
	}
	res.Audit = audit

	// 用户信息应该必须有
	var userInfo *entity.UserInfo
	err = dao.UserNuId.Ctx(ctx).Where(dao.UserInfo.Columns().UserId, res.UserId).Scan(&userInfo)
	if err != nil {
		return
	}
	if userInfo != nil {
		res.Nickname = userInfo.Nickname
	}

	return

}
