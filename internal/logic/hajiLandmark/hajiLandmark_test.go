package hajiLandmark

import (
	"context"
	"testing"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"

	"github.com/gogf/gf/v2/test/gtest"
	"github.com/gogf/gf/v2/util/gconv"
)

// TestHajiLandmarkTypeListItem_IconTypeAndUseCount 测试地标类型列表项的 IconType 和 UseCount 字段
func TestHajiLandmarkTypeListItem_IconTypeAndUseCount(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建服务实例
		service := New()
		ctx := context.Background()
		
		// 设置语言ID到上下文
		ctx = context.WithValue(ctx, consts.LanguageId, consts.ArticleLanguageZh)

		// 测试数据：创建一个地标类型
		createReq := &v1.HajiLandmarkTypeCreateReq{
			IconType: "mosque",
			Content: []v1.HajiLandmarkTypeCreateItem{
				{
					LanguageType: consts.ArticleLanguageZh,
					TypeName:     "测试清真寺类型",
				},
			},
		}

		// 创建地标类型
		createRes, err := service.TypeAdd(ctx, createReq)
		t.AssertNil(err)
		t.AssertGT(createRes.Id, 0)

		// 创建一个使用该类型的地标
		landmarkReq := &v1.HajiLandmarkCreateReq{
			InnerType: "destinasi",
			TypeId:    createRes.Id,
			Latitude:  "21.4225",
			Longitude: "39.8262",
			ImageUrl:  "https://example.com/image.jpg",
			Content: []v1.HajiLandmarkCreateItem{
				{
					LanguageType:     consts.ArticleLanguageZh,
					LandmarkName:     "测试地标",
					Country:          "沙特阿拉伯",
					ShortDescription: "测试地标描述",
					Address:          "麦加",
					InformationText:  "详细信息",
				},
			},
		}

		landmarkRes, err := service.Add(ctx, landmarkReq)
		t.AssertNil(err)
		t.AssertGT(landmarkRes.Id, 0)

		// 测试地标类型列表
		listReq := &v1.HajiLandmarkTypeListReq{
			ListReq: v1.ListReq{
				Page: 1,
				Size: 10,
			},
		}

		listRes, err := service.TypeList(ctx, listReq)
		t.AssertNil(err)
		t.AssertGT(len(listRes.List), 0)

		// 查找我们创建的地标类型
		var foundItem *v1.HajiLandmarkTypeListItem
		for _, item := range listRes.List {
			if item.Id == createRes.Id {
				foundItem = &item
				break
			}
		}

		t.AssertNE(foundItem, nil)
		t.AssertEQ(foundItem.IconType, "mosque")
		t.AssertEQ(foundItem.UseCount, 1) // 应该有1个地标使用这个类型
		t.AssertEQ(foundItem.TypeName, "测试清真寺类型")

		// 测试获取单个地标类型
		oneReq := &v1.HajiLandmarkTypeOneReq{
			Id: createRes.Id,
		}

		oneRes, err := service.TypeOne(ctx, oneReq)
		t.AssertNil(err)
		t.AssertEQ(oneRes.IconType, "mosque")
		t.AssertEQ(oneRes.UseCount, 1)
		t.AssertEQ(oneRes.TypeName, "测试清真寺类型")

		// 清理测试数据
		// 删除地标
		dao.HajiLandmark.Ctx(ctx).Where(dao.HajiLandmark.Columns().Id, landmarkRes.Id).Delete()
		dao.HajiLandmarkLanguages.Ctx(ctx).Where(dao.HajiLandmarkLanguages.Columns().LandmarkId, landmarkRes.Id).Delete()
		
		// 删除地标类型
		dao.HajiLandmarkType.Ctx(ctx).Where(dao.HajiLandmarkType.Columns().Id, createRes.Id).Delete()
		dao.HajiLandmarkTypeLanguages.Ctx(ctx).Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, createRes.Id).Delete()
	})
}

// TestHajiLandmarkTypeEdit_IconType 测试编辑地标类型时使用 IconType
func TestHajiLandmarkTypeEdit_IconType(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()
		ctx := context.Background()
		ctx = context.WithValue(ctx, consts.LanguageId, consts.ArticleLanguageZh)

		// 创建地标类型
		createReq := &v1.HajiLandmarkTypeCreateReq{
			IconType: "hotel",
			Content: []v1.HajiLandmarkTypeCreateItem{
				{
					LanguageType: consts.ArticleLanguageZh,
					TypeName:     "测试酒店类型",
				},
			},
		}

		createRes, err := service.TypeAdd(ctx, createReq)
		t.AssertNil(err)

		// 编辑地标类型
		editReq := &v1.HajiLandmarkTypeEditReq{
			Id:       createRes.Id,
			IconType: "restaurant",
			Content: []v1.HajiLandmarkTypeEditItem{
				{
					LanguageType: consts.ArticleLanguageZh,
					TypeName:     "测试餐厅类型",
				},
			},
		}

		_, err = service.TypeEdit(ctx, editReq)
		t.AssertNil(err)

		// 验证修改结果
		oneReq := &v1.HajiLandmarkTypeOneReq{
			Id: createRes.Id,
		}

		oneRes, err := service.TypeOne(ctx, oneReq)
		t.AssertNil(err)
		t.AssertEQ(oneRes.IconType, "restaurant")
		t.AssertEQ(oneRes.TypeName, "测试餐厅类型")

		// 清理测试数据
		dao.HajiLandmarkType.Ctx(ctx).Where(dao.HajiLandmarkType.Columns().Id, createRes.Id).Delete()
		dao.HajiLandmarkTypeLanguages.Ctx(ctx).Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, createRes.Id).Delete()
	})
}
