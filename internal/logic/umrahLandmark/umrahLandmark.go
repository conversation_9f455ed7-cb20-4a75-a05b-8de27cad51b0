package umrahLandmark

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/shopspring/decimal"
)

type sUmrahLandmark struct{}

func init() {
	service.RegisterUmrahLandmark(New())
}

func New() *sUmrahLandmark {
	return &sUmrahLandmark{}
}

// ==================== 地标类型相关接口实现 ====================

func (s *sUmrahLandmark) TypeList(ctx context.Context, req *v1.UmrahLandmarkTypeListReq) (out *v1.UmrahLandmarkTypeListRes, err error) {
	out = new(v1.UmrahLandmarkTypeListRes)
	out.Current = req.Current
	out.Offset = req.Offset

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	var types []entity.UmrahLandmarkType
	query := dao.UmrahLandmarkType.Ctx(ctx).OrderAsc(dao.UmrahLandmarkType.Columns().CreateTime)

	// 分页
	if req.Current > 0 && req.PageSize > 0 {
		query = query.Page(req.Current, req.PageSize)
	}

	err = query.Scan(&types)
	if err != nil {
		return nil, err
	}

	// 总数
	out.Total, err = dao.UmrahLandmarkType.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// 提取id
	typeIds := gutil.ListItemValuesUnique(types, "Id")

	var typeLanguages []entity.UmrahLandmarkTypeLanguages
	if len(typeIds) > 0 {
		err = dao.UmrahLandmarkTypeLanguages.Ctx(ctx).
			WhereIn(dao.UmrahLandmarkTypeLanguages.Columns().TypeId, typeIds).
			Scan(&typeLanguages)
		if err != nil {
			return nil, err
		}
	}

	typeLanguageMap := make(map[uint64][]entity.UmrahLandmarkTypeLanguages)
	for _, lang := range typeLanguages {
		typeLanguageMap[lang.TypeId] = append(typeLanguageMap[lang.TypeId], lang)
	}

	for _, typeItem := range types {
		item := &v1.UmrahLandmarkTypeListItem{
			Id:          int(typeItem.Id),
			IconUrl:     typeItem.IconUrl,
			LanguageArr: make([]v1.LanguageArrItem, 0),
		}

		for _, lang := range typeLanguageMap[typeItem.Id] {
			if int(lang.LanguageId) == languageId {
				item.TypeName = lang.TypeName
				break
			}
		}

		s.buildTypeLanguageArr(item, typeLanguageMap[typeItem.Id])

		out.List = append(out.List, *item)
	}

	return out, nil
}

// buildTypeLanguageArr 构建地标类型语言支持列表
func (s *sUmrahLandmark) buildTypeLanguageArr(item *v1.UmrahLandmarkTypeListItem, languages []entity.UmrahLandmarkTypeLanguages) {
	languageSupport := map[int]int{
		consts.ArticleLanguageZh: consts.Zero,
		consts.ArticleLanguageEn: consts.Zero,
		consts.ArticleLanguageId: consts.Zero,
	}

	// 检查支持的语言
	for _, lang := range languages {
		languageSupport[int(lang.LanguageId)] = consts.One
	}

	// 构建语言数组
	item.LanguageArr = []v1.LanguageArrItem{
		{
			LanguageType:     consts.ArticleLanguageZh,
			LanguageTypeText: "ZH",
			IsSupport:        languageSupport[consts.ArticleLanguageZh],
		},
		{
			LanguageType:     consts.ArticleLanguageEn,
			LanguageTypeText: "EN",
			IsSupport:        languageSupport[consts.ArticleLanguageEn],
		},
		{
			LanguageType:     consts.ArticleLanguageId,
			LanguageTypeText: "ID",
			IsSupport:        languageSupport[consts.ArticleLanguageId],
		},
	}
}

func (s *sUmrahLandmark) TypeAdd(ctx context.Context, req *v1.UmrahLandmarkTypeCreateReq) (out *v1.UmrahLandmarkTypeCreateRes, err error) {
	out = new(v1.UmrahLandmarkTypeCreateRes)

	// 检查类型名称是否重复
	// 这个循环一般最多3次，所以性能问题不大，这样写方便一些
	for _, item := range req.Content {
		count, err := dao.UmrahLandmarkTypeLanguages.Ctx(ctx).
			Where(dao.UmrahLandmarkTypeLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.UmrahLandmarkTypeLanguages.Columns().TypeName, item.TypeName).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("该类型名称已存在")
		}
	}

	var typeId int64
	err = dao.UmrahLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		typeId, err1 = tx.Model(dao.UmrahLandmarkType.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.UmrahLandmarkType{IconUrl: req.IconUrl}).
			InsertAndGetId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容
		var languages []do.UmrahLandmarkTypeLanguages
		for _, item := range req.Content {
			languages = append(languages, do.UmrahLandmarkTypeLanguages{
				TypeId:     typeId,
				LanguageId: item.LanguageType,
				TypeName:   item.TypeName,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.UmrahLandmarkTypeLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	out.Id = int(typeId)
	return out, nil
}

func (s *sUmrahLandmark) TypeEdit(ctx context.Context, req *v1.UmrahLandmarkTypeEditReq) (out *v1.UmrahLandmarkTypeEditRes, err error) {
	out = new(v1.UmrahLandmarkTypeEditRes)

	// 检查类型是否存在
	var typeItem entity.UmrahLandmarkType
	err = dao.UmrahLandmarkType.Ctx(ctx).
		Where(dao.UmrahLandmarkType.Columns().Id, req.Id).
		Scan(&typeItem)
	if err != nil {
		return nil, err
	}
	if typeItem.Id == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 检查类型名称是否重复（排除自己）
	for _, item := range req.Content {
		count, err := dao.UmrahLandmarkTypeLanguages.Ctx(ctx).
			Where(dao.UmrahLandmarkTypeLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.UmrahLandmarkTypeLanguages.Columns().TypeName, item.TypeName).
			WhereNot(dao.UmrahLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("该类型名称已存在")
		}
	}

	err = dao.UmrahLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err1 := tx.Model(dao.UmrahLandmarkType.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.UmrahLandmarkType{
				IconUrl: req.IconUrl,
			}).
			Where(dao.UmrahLandmarkType.Columns().Id, req.Id).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除旧的，插入新的
		_, err1 = tx.Model(dao.UmrahLandmarkTypeLanguages.Table()).Ctx(ctx).
			Where(dao.UmrahLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容
		var languages []do.UmrahLandmarkTypeLanguages
		for _, item := range req.Content {
			languages = append(languages, do.UmrahLandmarkTypeLanguages{
				TypeId:     req.Id,
				LanguageId: item.LanguageType,
				TypeName:   item.TypeName,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.UmrahLandmarkTypeLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (s *sUmrahLandmark) TypeOne(ctx context.Context, req *v1.UmrahLandmarkTypeOneReq) (out *v1.UmrahLandmarkTypeOneRes, err error) {
	var typeItem entity.UmrahLandmarkType
	err = dao.UmrahLandmarkType.Ctx(ctx).
		Where(dao.UmrahLandmarkType.Columns().Id, req.Id).
		Scan(&typeItem)
	if err != nil {
		return nil, err
	}
	if typeItem.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "地标类型不存在")
	}

	// 获取多语言内容
	var languages []entity.UmrahLandmarkTypeLanguages
	err = dao.UmrahLandmarkTypeLanguages.Ctx(ctx).
		Where(dao.UmrahLandmarkTypeLanguages.Columns().TypeId, req.Id).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 返回数据
	item := &v1.UmrahLandmarkTypeListItem{
		Id:          int(typeItem.Id),
		IconUrl:     typeItem.IconUrl,
		LanguageArr: make([]v1.LanguageArrItem, 0),
	}

	// 获取当前语言的内容
	languageId := gconv.Int(ctx.Value(consts.LanguageId))
	for _, lang := range languages {
		if int(lang.LanguageId) == languageId {
			item.TypeName = lang.TypeName
			break
		}
	}

	// 多语言
	s.buildTypeLanguageArr(item, languages)

	out = &v1.UmrahLandmarkTypeOneRes{
		UmrahLandmarkTypeListItem: *item,
	}
	return out, nil
}

func (s *sUmrahLandmark) TypeDelete(ctx context.Context, req *v1.UmrahLandmarkTypeDeleteReq) (out *v1.UmrahLandmarkTypeDeleteRes, err error) {
	out = new(v1.UmrahLandmarkTypeDeleteRes)

	// 检查类型是否存在
	count, err := dao.UmrahLandmarkType.Ctx(ctx).
		Where(dao.UmrahLandmarkType.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 检查该类型下是否有关联的地标
	landmarkCount, err := dao.UmrahLandmark.Ctx(ctx).
		Where(dao.UmrahLandmark.Columns().TypeId, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if landmarkCount > 0 {
		return nil, gerror.New("该地点类型下有关联内容，不允许删除！")
	}

	err = dao.UmrahLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除多语言内容
		_, err1 := tx.Model(dao.UmrahLandmarkTypeLanguages.Table()).Ctx(ctx).
			Where(dao.UmrahLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除type表记录
		_, err1 = tx.Model(dao.UmrahLandmarkType.Table()).Ctx(ctx).
			Where(dao.UmrahLandmarkType.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

// ==================== 地标相关接口实现 ====================

func (s *sUmrahLandmark) List(ctx context.Context, req *v1.UmrahLandmarkListReq) (out *v1.UmrahLandmarkListRes, err error) {
	out = new(v1.UmrahLandmarkListRes)
	out.Current = req.Current
	out.Offset = req.Offset

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	var landmarks []entity.UmrahLandmark
	query := dao.UmrahLandmark.Ctx(ctx).OrderAsc(dao.UmrahLandmark.Columns().Id)

	// 按类型筛选
	innerType := req.InnerType
	if len(req.InnerType) == 0 {
		innerType = "destinasi"
	}
	query = query.Where(dao.UmrahLandmark.Columns().InnerType, innerType)

	// 分页
	if req.Current > 0 && req.PageSize > 0 {
		query = query.Page(req.Current, req.PageSize)
	}

	err = query.Scan(&landmarks)
	if err != nil {
		return nil, err
	}

	// 总数
	countQuery := dao.UmrahLandmark.Ctx(ctx).Where(dao.UmrahLandmark.Columns().InnerType, innerType)
	out.Total, err = countQuery.Count()
	if err != nil {
		return nil, err
	}

	if len(landmarks) == 0 {
		return out, nil
	}

	// 先提取id
	landmarkIds := gutil.ListItemValuesUnique(landmarks, "Id")
	typeIds := gutil.ListItemValuesUnique(landmarks, "TypeId")

	// 获取地标多语言内容
	var landmarkLanguages []entity.UmrahLandmarkLanguages
	err = dao.UmrahLandmarkLanguages.Ctx(ctx).
		WhereIn(dao.UmrahLandmarkLanguages.Columns().LandmarkId, landmarkIds).
		Scan(&landmarkLanguages)
	if err != nil {
		return nil, err
	}

	// 获取地标类型多语言内容
	var typeLanguages []entity.UmrahLandmarkTypeLanguages
	if len(typeIds) > 0 {
		err = dao.UmrahLandmarkTypeLanguages.Ctx(ctx).
			WhereIn(dao.UmrahLandmarkTypeLanguages.Columns().TypeId, typeIds).
			Where(dao.UmrahLandmarkTypeLanguages.Columns().LanguageId, languageId).
			Scan(&typeLanguages)
		if err != nil {
			return nil, err
		}
	}

	landmarkLanguageMap := make(map[uint64][]entity.UmrahLandmarkLanguages)
	for _, lang := range landmarkLanguages {
		landmarkLanguageMap[lang.LandmarkId] = append(landmarkLanguageMap[lang.LandmarkId], lang)
	}

	typeLanguageMap := make(map[uint64]string)
	for _, lang := range typeLanguages {
		typeLanguageMap[lang.TypeId] = lang.TypeName
	}

	// 返回
	for _, landmark := range landmarks {
		item := &v1.UmrahLandmarkListItem{
			Id:          int(landmark.Id),
			TypeId:      int(landmark.TypeId),
			TypeName:    typeLanguageMap[landmark.TypeId],
			Latitude:    landmark.Latitude.String(),
			Longitude:   landmark.Longitude.String(),
			ImageUrl:    landmark.ImageUrl,
			LanguageArr: make([]v1.LanguageArrItem, 0),
		}

		// 设置当前语言的内容
		for _, lang := range landmarkLanguageMap[landmark.Id] {
			if int(lang.LanguageId) == languageId {
				item.LandmarkName = lang.LandmarkName
				item.ShortDescription = lang.ShortDescription
				item.Address = lang.Address
				break
			}
		}

		// 多语言
		s.buildLandmarkLanguageArr(item, landmarkLanguageMap[landmark.Id])

		out.List = append(out.List, *item)
	}

	return out, nil
}

// buildLandmarkLanguageArr 构建地标语言支持列表
func (s *sUmrahLandmark) buildLandmarkLanguageArr(item *v1.UmrahLandmarkListItem, languages []entity.UmrahLandmarkLanguages) {
	languageSupport := map[int]int{
		consts.ArticleLanguageZh: consts.Zero,
		consts.ArticleLanguageEn: consts.Zero,
		consts.ArticleLanguageId: consts.Zero,
	}

	// 检查支持的语言
	for _, lang := range languages {
		languageSupport[int(lang.LanguageId)] = consts.One
	}

	// 构建语言数组
	item.LanguageArr = []v1.LanguageArrItem{
		{
			LanguageType:     consts.ArticleLanguageZh,
			LanguageTypeText: "ZH",
			IsSupport:        languageSupport[consts.ArticleLanguageZh],
		},
		{
			LanguageType:     consts.ArticleLanguageEn,
			LanguageTypeText: "EN",
			IsSupport:        languageSupport[consts.ArticleLanguageEn],
		},
		{
			LanguageType:     consts.ArticleLanguageId,
			LanguageTypeText: "ID",
			IsSupport:        languageSupport[consts.ArticleLanguageId],
		},
	}
}

func (s *sUmrahLandmark) Add(ctx context.Context, req *v1.UmrahLandmarkCreateReq) (out *v1.UmrahLandmarkCreateRes, err error) {
	out = new(v1.UmrahLandmarkCreateRes)

	// 检查地标类型是否存在
	typeCount, err := dao.UmrahLandmarkType.Ctx(ctx).
		Where(dao.UmrahLandmarkType.Columns().Id, req.TypeId).
		Count()
	if err != nil {
		return nil, err
	}
	if typeCount == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 转换坐标
	latitude, err := decimal.NewFromString(req.Latitude)
	if err != nil {
		return nil, gerror.New("纬度格式不正确")
	}
	longitude, err := decimal.NewFromString(req.Longitude)
	if err != nil {
		return nil, gerror.New("经度格式不正确")
	}

	var landmarkId int64
	err = dao.UmrahLandmark.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		landmarkId, err1 = tx.Model(dao.UmrahLandmark.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.UmrahLandmark{
				TypeId:    req.TypeId,
				InnerType: "destinasi",
				Latitude:  latitude,
				Longitude: longitude,
				ImageUrl:  req.ImageUrl,
				SortOrder: 0, // 产品没有提出需要这个字段，先留空吧
			}).
			InsertAndGetId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容
		var languages []do.UmrahLandmarkLanguages
		for _, item := range req.Content {
			languages = append(languages, do.UmrahLandmarkLanguages{
				LandmarkId:       landmarkId,
				LanguageId:       item.LanguageType,
				LandmarkName:     item.LandmarkName,
				Country:          item.Country, // 后续可以通过Google Map API获取
				Address:          item.Address,
				ShortDescription: item.ShortDescription,
				InformationText:  item.InformationText,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.UmrahLandmarkLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	out.Id = int(landmarkId)
	return out, nil
}

// Edit 编辑地标
func (s *sUmrahLandmark) Edit(ctx context.Context, req *v1.UmrahLandmarkEditReq) (out *v1.UmrahLandmarkEditRes, err error) {
	out = new(v1.UmrahLandmarkEditRes)

	// 检查地标是否存在
	var landmark entity.UmrahLandmark
	err = dao.UmrahLandmark.Ctx(ctx).
		Where(dao.UmrahLandmark.Columns().Id, req.Id).
		Scan(&landmark)
	if err != nil {
		return nil, err
	}
	if landmark.Id == 0 {
		return nil, gerror.New("地标不存在")
	}

	// 检查地标类型是否存在
	typeCount, err := dao.UmrahLandmarkType.Ctx(ctx).
		Where(dao.UmrahLandmarkType.Columns().Id, req.TypeId).
		Count()
	if err != nil {
		return nil, err
	}
	if typeCount == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 转换坐标
	latitude, err := decimal.NewFromString(req.Latitude)
	if err != nil {
		return nil, gerror.New("纬度格式不正确")
	}
	longitude, err := decimal.NewFromString(req.Longitude)
	if err != nil {
		return nil, gerror.New("经度格式不正确")
	}

	err = dao.UmrahLandmark.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err1 := tx.Model(dao.UmrahLandmark.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.UmrahLandmark{
				TypeId:    req.TypeId,
				Latitude:  latitude,
				Longitude: longitude,
				ImageUrl:  req.ImageUrl,
				SortOrder: 0,
			}).
			Where(dao.UmrahLandmark.Columns().Id, req.Id).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除旧的，插入新的
		_, err1 = tx.Model(dao.UmrahLandmarkLanguages.Table()).Ctx(ctx).
			Where(dao.UmrahLandmarkLanguages.Columns().LandmarkId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容
		var languages []do.UmrahLandmarkLanguages
		for _, item := range req.Content {
			languages = append(languages, do.UmrahLandmarkLanguages{
				LandmarkId:       req.Id,
				LanguageId:       item.LanguageType,
				LandmarkName:     item.LandmarkName,
				Country:          "", // 暂时留空，后续可以通过Google Map API获取
				Address:          item.Address,
				ShortDescription: item.ShortDescription,
				InformationText:  item.InformationText,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.UmrahLandmarkLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (s *sUmrahLandmark) One(ctx context.Context, req *v1.UmrahLandmarkOneReq) (out *v1.UmrahLandmarkOneRes, err error) {
	var landmark entity.UmrahLandmark
	err = dao.UmrahLandmark.Ctx(ctx).
		Where(dao.UmrahLandmark.Columns().Id, req.Id).
		Scan(&landmark)
	if err != nil {
		return nil, err
	}
	if landmark.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "地标不存在")
	}

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	// 获取多语言内容
	var languages []entity.UmrahLandmarkLanguages
	err = dao.UmrahLandmarkLanguages.Ctx(ctx).
		Where(dao.UmrahLandmarkLanguages.Columns().LandmarkId, req.Id).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 获取地标类型名称
	var typeLanguage entity.UmrahLandmarkTypeLanguages
	err = dao.UmrahLandmarkTypeLanguages.Ctx(ctx).
		Where(dao.UmrahLandmarkTypeLanguages.Columns().TypeId, landmark.TypeId).
		Where(dao.UmrahLandmarkTypeLanguages.Columns().LanguageId, languageId).
		Scan(&typeLanguage)
	if err != nil {
		return nil, err
	}

	// 构建返回数据
	item := &v1.UmrahLandmarkDetail{
		Id:        int(landmark.Id),
		TypeId:    int(landmark.TypeId),
		InnerType: landmark.InnerType,
		Latitude:  landmark.Latitude.String(),
		Longitude: landmark.Longitude.String(),
		ImageUrl:  landmark.ImageUrl,
		Content:   make([]v1.UmrahLandmarkItem, 0),
	}

	// 设置当前语言的内容
	for _, lang := range languages {
		if int(lang.LanguageId) == languageId {
			item.Content = append(item.Content, v1.UmrahLandmarkItem{
				LanguageType:     int(lang.LanguageId),
				LandmarkName:     lang.LandmarkName,
				Country:          lang.Country,
				ShortDescription: lang.ShortDescription,
				Address:          lang.Address,
				InformationText:  lang.InformationText,
			})
			break
		}
	}

	out = &v1.UmrahLandmarkOneRes{
		UmrahLandmarkDetail: *item,
	}
	return out, nil
}

func (s *sUmrahLandmark) Delete(ctx context.Context, req *v1.UmrahLandmarkDeleteReq) (out *v1.UmrahLandmarkDeleteRes, err error) {
	out = new(v1.UmrahLandmarkDeleteRes)

	// 检查地标是否存在
	count, err := dao.UmrahLandmark.Ctx(ctx).
		Where(dao.UmrahLandmark.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("地标不存在")
	}

	err = dao.UmrahLandmark.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除多语言内容
		_, err1 := tx.Model(dao.UmrahLandmarkLanguages.Table()).Ctx(ctx).
			Where(dao.UmrahLandmarkLanguages.Columns().LandmarkId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除主表记录
		_, err1 = tx.Model(dao.UmrahLandmark.Table()).Ctx(ctx).
			Where(dao.UmrahLandmark.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}
