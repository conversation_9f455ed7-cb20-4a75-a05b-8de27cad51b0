package systemSetting

import (
	"context"
	"database/sql"
	"errors"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	"gtcms/internal/service"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"

	dao "gtcms/internal/dao/user_account_svc"
	entity "gtcms/internal/model/entity/user_account_svc"
)

type sSystemSetting struct{}

func init() {
	service.RegisterSystemSetting(New())
}

func New() *sSystemSetting {
	return &sSystemSetting{}
}

// 用户协议设置
func (s *sSystemSetting) UserProtocolSet(ctx context.Context, req *v1.UserProtocolSetReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= len(req.ProtocolArr) {
		return out, gerror.New("参数错误")
	}
	total, err := dao.UserProtocol.Ctx(ctx).Where(dao.UserProtocol.Columns().Type, consts.Zero).Count()
	if err != nil {
		return out, err
	}
	// 设置
	err = dao.UserProtocol.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 写入UserProtocol表
		var err1 error
		if consts.Zero >= total {
			_, err1 = dao.UserProtocol.Ctx(ctx).Data(g.Map{
				dao.UserProtocol.Columns().Type: consts.Zero,
			}).Insert()
		}
		// 先把原来的数据删掉
		_, err1 = dao.UserProtocolData.Ctx(ctx).Where(dao.UserProtocolData.Columns().TypeId, consts.Zero).Delete()
		if err1 != nil {
			return err1
		}
		// 批量插入
		list := g.List{}
		for _, item := range req.ProtocolArr {
			list = append(list, g.Map{
				dao.UserProtocolData.Columns().TypeId:     consts.Zero,
				dao.UserProtocolData.Columns().LanguageId: item.LanguageType,
				dao.UserProtocolData.Columns().Content:    item.Content,
			})
		}
		if len(list) <= consts.Zero {
			return nil
		}
		_, err1 = dao.UserProtocolData.Ctx(ctx).Data(list).Insert()
		return err1
	})
	return out, err
}

// 隐私政策设置
func (s *sSystemSetting) PrivacyPolicySet(ctx context.Context, req *v1.PrivacyPolicySetReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= len(req.PrivacyPolicyArr) {
		return out, gerror.New("参数错误")
	}
	total, err := dao.UserProtocol.Ctx(ctx).Where(dao.UserProtocol.Columns().Type, consts.One).Count()
	if err != nil {
		return out, err
	}
	// 设置
	err = dao.UserProtocol.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 写入UserProtocol表
		var err1 error
		if consts.Zero >= total {
			_, err1 = dao.UserProtocol.Ctx(ctx).Data(g.Map{
				dao.UserProtocol.Columns().Type: consts.One,
			}).Insert()
		}
		// 先把原来的数据删掉
		_, err1 = dao.UserProtocolData.Ctx(ctx).Where(dao.UserProtocolData.Columns().TypeId, consts.One).Delete()
		if err1 != nil {
			return err1
		}
		// 批量插入
		list := g.List{}
		for _, item := range req.PrivacyPolicyArr {
			list = append(list, g.Map{
				dao.UserProtocolData.Columns().TypeId:     consts.One,
				dao.UserProtocolData.Columns().LanguageId: item.LanguageType,
				dao.UserProtocolData.Columns().Content:    item.Content,
			})
		}
		if len(list) <= consts.Zero {
			return nil
		}
		_, err1 = dao.UserProtocolData.Ctx(ctx).Data(list).Insert()
		return err1
	})
	return out, err
}

// 用户协议详情
func (s *sSystemSetting) UserProtocolInfo(ctx context.Context, req *v1.UserProtocolInfoReq) (out *v1.UserProtocolInfoRes, err error) {
	out = new(v1.UserProtocolInfoRes)
	var list []*entity.UserProtocolData
	// 获取数据
	err = dao.UserProtocolData.Ctx(ctx).Where(dao.UserProtocolData.Columns().TypeId, consts.Zero).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	for _, item := range list {
		out.ProtocolArr = append(out.ProtocolArr, v1.ProtocolArrItem{
			Content:      item.Content,
			LanguageType: gconv.Int(item.LanguageId),
		})
	}
	return out, nil
}

// 隐私政策详情
func (s *sSystemSetting) PrivacyPolicyInfo(ctx context.Context, req *v1.PrivacyPolicyInfoReq) (out *v1.PrivacyPolicyInfoRes, err error) {
	out = new(v1.PrivacyPolicyInfoRes)
	var list []*entity.UserProtocolData
	// 获取数据
	err = dao.UserProtocolData.Ctx(ctx).Where(dao.UserProtocolData.Columns().TypeId, consts.One).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	for _, item := range list {
		out.PrivacyPolicyArr = append(out.PrivacyPolicyArr, v1.PrivacyPolicyItem{
			Content:      item.Content,
			LanguageType: gconv.Int(item.LanguageId),
		})
	}
	return out, nil
}

// 安全设置(短信及WhatsAPP验证码机制)
func (s *sSystemSetting) SafetySettingSet(ctx context.Context, req *v1.SafetySettingSetReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= req.MobileInterval {
		return out, gerror.New("单手机号发送频率（秒）为必填项")
	}
	if 1 > req.MobileInterval || 600 < req.MobileInterval {
		return out, gerror.New("单手机号发送频率（秒）输入范围为 1 到 600")
	}
	if 0 >= req.MobileIntervalMaxSend {
		return out, gerror.New("单手机号发送频率（次）为必填项")
	}
	if 1 > req.MobileIntervalMaxSend || 10 < req.MobileIntervalMaxSend {
		return out, gerror.New("单手机号发送频率（次）输入范围为 1 到 10")
	}
	if 0 >= req.MobileDailyLimit {
		return out, gerror.New("单手机号每日限额必填项")
	}
	if 1 > req.MobileDailyLimit || 100 < req.MobileDailyLimit {
		return out, gerror.New("单手机号每日限额输入范围为 1 到 100")
	}
	if 0 >= req.IpHourlyMobileLimit {
		return out, gerror.New("单IP每小时限额必填项")
	}
	if 1 > req.IpHourlyMobileLimit || 500 < req.IpHourlyMobileLimit {
		return out, gerror.New("单手机号每小时限额输入范围为 1 到 500")
	}
	if 0 >= req.IpDailySendLimit {
		return out, gerror.New("单IP每天限额必填项")
	}
	if 1 > req.IpDailySendLimit || 1000 < req.IpDailySendLimit {
		return out, gerror.New("单IP每天限额输入范围为 1 到 1000")
	}
	if 0 >= req.MobileIpMinute {
		return out, gerror.New("相同手机号+IP组合（分钟）为必填项")
	}
	if 1 > req.MobileIpMinute || 60 < req.MobileIpMinute {
		return out, gerror.New("相同手机号+IP组合（分钟）输入范围为 1 到 60")
	}
	if 0 >= req.MobileIpMaxRetry {
		return out, gerror.New("相同手机号+IP组合（次数）为必填项")
	}
	if 1 > req.MobileIpMaxRetry || 10 < req.MobileIpMaxRetry {
		return out, gerror.New("相同手机号+IP组合（次数）输入范围为 1 到 10")
	}
	// 记录map
	reqMap := map[string]int{
		consts.MobileInterval:        req.MobileInterval,
		consts.MobileIntervalMaxSend: req.MobileIntervalMaxSend,
		consts.MobileDailyLimit:      req.MobileDailyLimit,
		consts.IpHourlyMobileLimit:   req.IpHourlyMobileLimit,
		consts.IpDailySendLimit:      req.IpDailySendLimit,
		consts.MobileIpMinute:        req.MobileIpMinute,
		consts.MobileIpMaxRetry:      req.MobileIpMaxRetry,
	}
	currentTime := time.Now().UnixMilli()
	err = dao.UserSafetyConfig.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		var list []*entity.UserSafetyConfig
		err1 = dao.UserSafetyConfig.Ctx(ctx).
			Where(dao.UserSafetyConfig.Columns().ConfigType, consts.ConfigTypeSms).
			Scan(&list)
		if err1 != nil && !errors.Is(err1, sql.ErrNoRows) {
			return err1
		}
		configKeys := gconv.Strings(gutil.ListItemValuesUnique(list, "ConfigKey"))
		configKeyArr := []string{
			consts.MobileInterval,
			consts.MobileIntervalMaxSend,
			consts.MobileDailyLimit,
			consts.IpHourlyMobileLimit,
			consts.IpDailySendLimit,
			consts.MobileIpMinute,
			consts.MobileIpMaxRetry,
		}
		for _, item := range configKeyArr {
			// 判断是否存在，如果存在则更新，否则插入
			configVal := reqMap[item]
			desc := consts.GetConfigDesc(item)
			if gstr.InArray(configKeys, item) {
				_, err1 = dao.UserSafetyConfig.Ctx(ctx).
					Where(dao.UserSafetyConfig.Columns().ConfigKey, item).
					Where(dao.UserSafetyConfig.Columns().ConfigType, consts.ConfigTypeSms).
					Data(g.Map{
						dao.UserSafetyConfig.Columns().ConfigValue: configVal,
						dao.UserSafetyConfig.Columns().Description: desc,
						dao.UserSafetyConfig.Columns().UpdateTime:  currentTime,
					}).
					Update()
			} else {
				_, err1 = dao.UserSafetyConfig.Ctx(ctx).
					Data(g.Map{
						dao.UserSafetyConfig.Columns().ConfigKey:   item,
						dao.UserSafetyConfig.Columns().ConfigValue: configVal,
						dao.UserSafetyConfig.Columns().ConfigType:  consts.ConfigTypeSms,
						dao.UserSafetyConfig.Columns().Description: desc,
						dao.UserSafetyConfig.Columns().CreateTime:  currentTime,
					}).
					Insert()
			}
			if err1 != nil {
				return err1
			}
		}
		return err1
	})
	return out, err
}

// 安全设置详情(短信及WhatsAPP验证码机制)
func (s *sSystemSetting) SafetySettingInfo(ctx context.Context, req *v1.SafetySettingInfoReq) (out *v1.SafetySettingInfoRes, err error) {
	out = new(v1.SafetySettingInfoRes)
	var list []*entity.UserSafetyConfig
	err = dao.UserSafetyConfig.Ctx(ctx).
		Where(dao.UserSafetyConfig.Columns().ConfigType, consts.ConfigTypeSms).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	if consts.Zero >= len(list) {
		return out, nil
	}
	for _, item := range list {
		switch item.ConfigKey {
		case consts.MobileInterval:
			out.MobileInterval = gconv.Int(item.ConfigValue)
		case consts.MobileIntervalMaxSend:
			out.MobileIntervalMaxSend = gconv.Int(item.ConfigValue)
		case consts.MobileDailyLimit:
			out.MobileDailyLimit = gconv.Int(item.ConfigValue)
		case consts.IpHourlyMobileLimit:
			out.IpHourlyMobileLimit = gconv.Int(item.ConfigValue)
		case consts.IpDailySendLimit:
			out.IpDailySendLimit = gconv.Int(item.ConfigValue)
		case consts.MobileIpMinute:
			out.MobileIpMinute = gconv.Int(item.ConfigValue)
		case consts.MobileIpMaxRetry:
			out.MobileIpMaxRetry = gconv.Int(item.ConfigValue)
		}
	}
	return out, nil
}
