package systemSetting

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 用户协议
func (c *Controller) UserProtocolSet(ctx context.Context, req *v1.UserProtocolSetReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.SystemSetting().UserProtocolSet(ctx, req)
	return
}

// 用户协议详情
func (c *Controller) UserProtocolInfo(ctx context.Context, req *v1.UserProtocolInfoReq) (res *v1.UserProtocolInfoRes, err error) {
	res, err = service.SystemSetting().UserProtocolInfo(ctx, req)
	return
}

// 隐私政策
func (c *Controller) PrivacyPolicySet(ctx context.Context, req *v1.PrivacyPolicySetReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.SystemSetting().PrivacyPolicySet(ctx, req)
	return
}

// 隐私政策详情
func (c *Controller) PrivacyPolicyInfo(ctx context.Context, req *v1.PrivacyPolicyInfoReq) (res *v1.PrivacyPolicyInfoRes, err error) {
	res, err = service.SystemSetting().PrivacyPolicyInfo(ctx, req)
	return
}

// 安全设置(短信及WhatsAPP验证码机制)
func (c *Controller) SafetySettingSet(ctx context.Context, req *v1.SafetySettingSetReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.SystemSetting().SafetySettingSet(ctx, req)
	return
}

// 安全设置详情
func (c *Controller) SafetySettingInfo(ctx context.Context, req *v1.SafetySettingInfoReq) (res *v1.SafetySettingInfoRes, err error) {
	res = new(v1.SafetySettingInfoRes)
	res, err = service.SystemSetting().SafetySettingInfo(ctx, req)
	return
}
