// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	ISystemSetting interface {
		// 用户协议设置
		UserProtocolSet(ctx context.Context, req *v1.UserProtocolSetReq) (out *v1.EmptyDataRes, err error)
		// 隐私政策设置
		PrivacyPolicySet(ctx context.Context, req *v1.PrivacyPolicySetReq) (out *v1.EmptyDataRes, err error)
		// 用户协议详情
		UserProtocolInfo(ctx context.Context, req *v1.UserProtocolInfoReq) (out *v1.UserProtocolInfoRes, err error)
		// 隐私政策详情
		PrivacyPolicyInfo(ctx context.Context, req *v1.PrivacyPolicyInfoReq) (out *v1.PrivacyPolicyInfoRes, err error)
		// 安全设置(短信及WhatsAPP验证码机制)
		SafetySettingSet(ctx context.Context, req *v1.SafetySettingSetReq) (out *v1.EmptyDataRes, err error)
		// 安全设置详情(短信及WhatsAPP验证码机制)
		SafetySettingInfo(ctx context.Context, req *v1.SafetySettingInfoReq) (out *v1.SafetySettingInfoRes, err error)
	}
)

var (
	localSystemSetting ISystemSetting
)

func SystemSetting() ISystemSetting {
	if localSystemSetting == nil {
		panic("implement not found for interface ISystemSetting, forgot register?")
	}
	return localSystemSetting
}

func RegisterSystemSetting(i ISystemSetting) {
	localSystemSetting = i
}
