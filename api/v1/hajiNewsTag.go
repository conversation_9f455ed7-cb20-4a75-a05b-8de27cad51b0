package v1

import "github.com/gogf/gf/v2/frame/g"

type HajiNewsTag struct {
	NameArr    []HajiNewsTagNameArrItem `v:"required" json:"name_arr" dc:"标签名称数组"`
	SortOrder  int                      `v:"required|between:0,9999" json:"sort_order" dc:"排序值，数字越小排序越靠前"`
	ArticleArr []int                    `json:"article_arr" dc:"关联文章ID数组"`
}

type HajiNewsTagNameArrItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	TagName      string `v:"required|length:1,60" json:"tag_name" dc:"标签名称"`
}

// 新增朝觐新闻标签
type HajiNewsTagAddReq struct {
	g.Meta `path:"/hajiNewsTag/add" method:"post" tags:"朝觐新闻标签" summary:"新增朝觐新闻标签"`
	HajiNewsTag
}

type HajiNewsTagAddRes struct{}

// 编辑朝觐新闻标签
type HajiNewsTagEditReq struct {
	g.Meta `path:"/hajiNewsTag/edit" method:"post" tags:"朝觐新闻标签" summary:"编辑朝觐新闻标签"`
	Id     int `v:"required" json:"id" dc:"标签ID"`
	HajiNewsTag
}

type HajiNewsTagEditRes struct{}

// 朝觐新闻标签详情
type HajiNewsTagInfoReq struct {
	g.Meta `path:"/hajiNewsTag/info" method:"get,post" tags:"朝觐新闻标签" summary:"朝觐新闻标签详情"`
	Id     int `v:"required" json:"id" dc:"标签ID"`
}

type HajiNewsTagInfoRes struct {
	Id         int                      `json:"id" dc:"标签ID"`
	NameArr    []HajiNewsTagNameArrItem `json:"name_arr" dc:"标签名称数组"`
	SortOrder  int                      `json:"sort_order" dc:"排序值"`
	ArticleArr []int                    `json:"article_arr" dc:"关联文章ID数组"`
}

// 朝觐新闻标签列表
type HajiNewsTagListReq struct {
	g.Meta `path:"/hajiNewsTag/list" method:"get,post" tags:"朝觐新闻标签" summary:"朝觐新闻标签列表"`
	ListReq
}

type HajiNewsTagListRes struct {
	List []HajiNewsTagListItem `json:"list"`
	ListRes
}

type HajiNewsTagListItem struct {
	Id           int               `json:"id" dc:"标签ID"`
	TagName      string            `json:"tag_name" dc:"标签名称（当前语言）"`
	SortOrder    int               `json:"sort_order" dc:"排序值"`
	ArticleCount int               `json:"article_count" dc:"关联文章数"`
	LanguageArr  []LanguageArrItem `json:"language_arr" dc:"语言列表"`
	CreateTime   int64             `json:"create_time" dc:"创建时间"`
}

// 删除朝觐新闻标签
type HajiNewsTagDeleteReq struct {
	g.Meta `path:"/hajiNewsTag/delete" method:"post" tags:"朝觐新闻标签" summary:"删除朝觐新闻标签"`
	Id     int `v:"required" json:"id" dc:"标签ID"`
}

type HajiNewsTagDeleteRes struct{}
