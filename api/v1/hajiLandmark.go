package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ==================== 地标类型相关接口 ====================

// HajiLandmarkTypeListReq 地标类型列表请求
type HajiLandmarkTypeListReq struct {
	g.Meta `path:"/hajiLandmarkType/list" method:"post" tags:"地标管理" summary:"地标类型列表"`
	ListReq
}

type HajiLandmarkTypeListItem struct {
	Id          int               `json:"id" dc:"类型ID"`
	IconType    string            `json:"icon_type" dc:"图标类型"`
	TypeName    string            `json:"type_name" dc:"类型名称"`
	UseCount    int               `json:"use_count" dc:"使用次数"`
	LanguageArr []LanguageArrItem `json:"language_arr" dc:"支持的语言列表"`
}

type HajiLandmarkTypeListRes struct {
	List []HajiLandmarkTypeListItem `json:"list" dc:"类型列表"`
	ListRes
}

// HajiLandmarkTypeCreateReq 新增地标类型请求
type HajiLandmarkTypeCreateReq struct {
	g.Meta   `path:"/hajiLandmarkType/add" method:"post" tags:"地标管理" summary:"新增地标类型"`
	IconType string                       `v:"required" json:"icon_type" dc:"图标类型"`
	Content  []HajiLandmarkTypeCreateItem `v:"required|array|required" json:"content" dc:"类型内容"`
}

type HajiLandmarkTypeCreateItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TypeName     string `v:"required|length:1,30" json:"type_name" dc:"类型名称"`
}

type HajiLandmarkTypeCreateRes struct {
	Id int `json:"id" dc:"类型ID"`
}

// HajiLandmarkTypeEditReq 编辑地标类型请求
type HajiLandmarkTypeEditReq struct {
	g.Meta   `path:"/hajiLandmarkType/edit" method:"post" tags:"地标管理" summary:"编辑地标类型"`
	Id       int                        `v:"required" json:"id" dc:"类型ID"`
	IconType string                     `v:"required" json:"icon_type" dc:"图标类型"`
	Content  []HajiLandmarkTypeEditItem `v:"required|array|required" json:"content" dc:"类型内容"`
}

type HajiLandmarkTypeEditItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TypeName     string `v:"required|length:1,30" json:"type_name" dc:"类型名称"`
}

type HajiLandmarkTypeEditRes struct{}

// HajiLandmarkTypeOneReq 获取单个地标类型请求
type HajiLandmarkTypeOneReq struct {
	g.Meta `path:"/hajiLandmarkType/one" method:"post" tags:"地标管理" summary:"获取单个地标类型"`
	Id     int `v:"required" json:"id" dc:"类型ID"`
}

type HajiLandmarkTypeOneRes struct {
	HajiLandmarkTypeListItem
}

// HajiLandmarkTypeDeleteReq 删除地标类型请求
type HajiLandmarkTypeDeleteReq struct {
	g.Meta `path:"/hajiLandmarkType/delete" method:"post" tags:"地标管理" summary:"删除地标类型"`
	Id     int `v:"required" json:"id" dc:"类型ID"`
}

type HajiLandmarkTypeDeleteRes struct{}

// ==================== 地标相关接口 ====================

// HajiLandmarkListReq 地标列表请求
type HajiLandmarkListReq struct {
	g.Meta    `path:"/hajiLandmark/list" method:"post" tags:"地标管理" summary:"地标列表"`
	InnerType string `json:"inner_type" dc:"内部类型: (destinasi, tokoh)"`
	ListReq
}

type HajiLandmarkListItem struct {
	Id               int               `json:"id" dc:"地标ID"`
	TypeId           int               `json:"type_id" dc:"地标类型ID"`
	TypeName         string            `json:"type_name" dc:"地标类型名称"`
	LandmarkName     string            `json:"landmark_name" dc:"地标名称"`
	ShortDescription string            `json:"short_description" dc:"地标简介"`
	Country          string            `json:"country" dc:"国家/地区"`
	Address          string            `json:"address" dc:"地址"`
	Latitude         string            `json:"latitude" dc:"纬度"`
	Longitude        string            `json:"longitude" dc:"经度"`
	ImageUrl         string            `json:"image_url" dc:"图片URL"`
	LanguageArr      []LanguageArrItem `json:"language_arr" dc:"支持的语言列表"`
}

type HajiLandmarkListRes struct {
	List []HajiLandmarkListItem `json:"list" dc:"地标列表"`
	ListRes
}

// HajiLandmarkCreateReq 新增地标请求
type HajiLandmarkCreateReq struct {
	g.Meta    `path:"/hajiLandmark/add" method:"post" tags:"地标管理" summary:"新增地标"`
	InnerType string                   `v:"required|in:destinasi,tokoh" json:"inner_type" dc:"内部类型: (destinasi, tokoh)"`
	TypeId    int                      `v:"required" json:"type_id" dc:"类型ID"`
	Latitude  string                   `v:"required" json:"latitude" dc:"纬度"`
	Longitude string                   `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string                   `v:"required" json:"image_url" dc:"图片URL"`
	Content   []HajiLandmarkCreateItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type HajiLandmarkCreateItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	Country          string `v:"required|length:1,100" json:"country" dc:"国家/地区"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

type HajiLandmarkCreateRes struct {
	Id int `json:"id" dc:"地标ID"`
}

// HajiLandmarkEditReq 编辑地标请求
type HajiLandmarkEditReq struct {
	g.Meta    `path:"/hajiLandmark/edit" method:"post" tags:"地标管理" summary:"编辑地标"`
	Id        int                    `v:"required" json:"id" dc:"地标ID"`
	TypeId    int                    `v:"required" json:"type_id" dc:"类型ID"`
	Latitude  string                 `v:"required" json:"latitude" dc:"纬度"`
	Longitude string                 `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string                 `v:"required" json:"image_url" dc:"图片URL"`
	Content   []HajiLandmarkEditItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type HajiLandmarkEditItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	Country          string `v:"required|length:1,100" json:"country" dc:"国家/地区"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

type HajiLandmarkEditRes struct{}

// HajiLandmarkOneReq 获取单个地标请求
type HajiLandmarkOneReq struct {
	g.Meta `path:"/hajiLandmark/one" method:"post" tags:"地标管理" summary:"获取单个地标"`
	Id     int `v:"required" json:"id" dc:"地标ID"`
}

type HajiLandmarkOneRes struct {
	HajiLandmarkDetail
}

type HajiLandmarkDetail struct {
	Id        int                `v:"required" json:"id" dc:"地标ID"`
	InnerType string             `v:"required|in:destinasi,tokoh" json:"inner_type" dc:"内部类型: (destinasi, tokoh)"`
	TypeId    int                `v:"required" json:"type_id" dc:"类型ID"`
	Latitude  string             `v:"required" json:"latitude" dc:"纬度"`
	Longitude string             `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string             `v:"required" json:"image_url" dc:"图片URL"`
	Content   []HajiLandmarkItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type HajiLandmarkItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	Country          string `v:"required|length:1,100" json:"country" dc:"国家/地区"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

// HajiLandmarkDeleteReq 删除地标请求
type HajiLandmarkDeleteReq struct {
	g.Meta `path:"/hajiLandmark/delete" method:"post" tags:"地标管理" summary:"删除地标"`
	Id     int `v:"required" json:"id" dc:"地标ID"`
}

type HajiLandmarkDeleteRes struct{}
