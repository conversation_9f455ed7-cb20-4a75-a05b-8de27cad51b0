package v1

import "github.com/gogf/gf/v2/frame/g"

type UserProtocolSetReq struct {
	g.Meta      `path:"/systemSetting/userProtocol/set" method:"post" tags:"系统设置" summary:"用户协议设置"`
	ProtocolArr []ProtocolArrItem `v:"required" json:"protocol_arr" dc:"协议内容数组"`
}

type UserProtocolInfoReq struct {
	g.Meta `path:"/systemSetting/userProtocol/info" method:"post,get" tags:"系统设置" summary:"用户协议详情"`
}

type UserProtocolInfoRes struct {
	ProtocolArr []ProtocolArrItem `json:"protocol_arr" dc:"协议内容数组"`
}

type ProtocolArrItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	Content      string `v:"required" json:"content" dc:"内容"`
}

type PrivacyPolicySetReq struct {
	g.Meta           `path:"/systemSetting/privacyPolicy/set" method:"post" tags:"系统设置" summary:"隐私政策"`
	PrivacyPolicyArr []PrivacyPolicyItem `v:"required" json:"privacy_policy_arr" dc:"隐私政策内容数组"`
}

type PrivacyPolicyInfoReq struct {
	g.Meta `path:"/systemSetting/privacyPolicy/info" method:"post,get" tags:"系统设置" summary:"用户协议详情"`
}

type PrivacyPolicyInfoRes struct {
	PrivacyPolicyArr []PrivacyPolicyItem `json:"protocol_arr" dc:"隐私政策内容数组"`
}

type PrivacyPolicyItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	Content      string `v:"required" json:"content" dc:"内容"`
}

type SafetySettingSetReq struct {
	g.Meta `path:"/systemSetting/safetySetting/set" method:"post" tags:"系统设置" summary:"安全设置"`
	SafetySetting
}

type SafetySettingInfoReq struct {
	g.Meta `path:"/systemSetting/safetySetting/info" method:"post" tags:"系统设置" summary:"安全设置"`
}

type SafetySettingInfoRes struct {
	SafetySetting
}

type SafetySetting struct {
	MobileInterval        int `json:"mobile_interval" dc:"同一手机号发送间隔(秒)"`
	MobileIntervalMaxSend int `json:"mobile_interval_max_send" dc:"同一手机号间隔内最多发送次数"`
	MobileDailyLimit      int `json:"mobile_daily_limit" dc:"单手机号每日限额"`
	IpHourlyMobileLimit   int `json:"ip_hourly_mobile_limit" dc:"单IP每小时手机号数量限额"`
	IpDailySendLimit      int `json:"ip_daily_send_limit" dc:"单IP每日发送数量限额"`
	MobileIpMinute        int `json:"mobile_ip_minute" dc:"单手机号IP时间范围（分钟）"`
	MobileIpMaxRetry      int `json:"mobile_ip_max_retry" dc:"单手机号IP最大重试次数"`
}
